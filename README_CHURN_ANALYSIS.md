# Comprehensive Churn Rate Analysis Following <PERSON>'s Methodology

## 🎯 Overview

This project implements a comprehensive churn rate analysis following **<PERSON>'s methodology** as outlined in her seminal work on cohort retention analysis. The analysis is specifically adapted for the Russian Orders Dataset (January-June 2025) and provides actionable insights for customer retention strategies.

## 📊 Methodology Overview

### <PERSON>'s Approach

<PERSON> is a renowned data analyst known for her comprehensive approach to churn and retention analysis. Her methodology emphasizes:

1. **Precise Customer Definition**: Clearly defining what constitutes an "active" customer
2. **Business-Specific Churn Definition**: Adapting churn definitions to business context
3. **Cohort-Based Analysis**: Tracking customer behavior over time periods
4. **Multiple Churn Rate Calculations**: Simple vs. adjusted churn rates
5. **Comprehensive Segmentation**: Breaking down churn by customer segments
6. **Predictive Indicators**: Identifying early warning signs
7. **Actionable Insights**: Providing specific, implementable recommendations

### Key Publications Referenced
- "How to measure cohort retention" (<PERSON>'s Newsletter, 2022)
- "Measuring Non-Cohorted Retention or Blended Churn" (Data Analysis Journal, 2024)
- Various articles on customer retention and cohort analysis

## 🚀 Analysis Components

### 1. Data Preparation
- **Data Loading**: Combines multiple CSV files from January-June 2025
- **Data Cleaning**: Removes cancelled/refunded orders, handles outliers
- **Customer Definition**: Uses "purchase" as the main user action (following Berezovsky's recommendation)

### 2. Churn Definition
- **Transactional Business Adaptation**: Defines churn as 60+ days of inactivity
- **Customer Status Classification**: Active, At Risk, Churned
- **Business Context**: Adapted for e-commerce transactional data

### 3. Cohort Analysis
- **Acquisition Cohorts**: Groups customers by first purchase month
- **Retention Tracking**: Monitors customer activity over subsequent months
- **Cohort Retention Rates**: Calculates retention using Berezovsky's formulas

### 4. Churn Rate Calculations
- **Simple Churn Rate**: `(Customers lost during period) / (Customers at start of period)`
- **Adjusted Churn Rate**: Accounts for new customer acquisitions
- **Monthly Trends**: Tracks churn rates over time

### 5. Segmentation Analysis
- **Regional Segmentation**: Churn rates by geographic region
- **Customer Lifecycle**: One-time, Occasional, Regular, Loyal customers
- **RFM Analysis**: Recency, Frequency, Monetary value segmentation

### 6. Time-Based Analysis
- **Monthly Trends**: Churn patterns across months
- **Seasonal Analysis**: Seasonal churn variations
- **Day-of-Week Analysis**: Acquisition day impact on churn

### 7. Predictive Indicators
- **Correlation Analysis**: Identifies factors most correlated with churn
- **Early Warning Signs**: Metrics for identifying at-risk customers
- **Leading Indicators**: Proactive churn prediction metrics

### 8. Comprehensive Visualizations
- **Cohort Retention Heatmap**: Berezovsky's signature visualization
- **Churn Rate Trends**: Simple vs. adjusted churn rates
- **Segmentation Charts**: Visual breakdown by customer segments
- **Interactive Dashboards**: Plotly-based interactive visualizations

### 9. Actionable Insights
- **Immediate Actions**: Urgent steps to reduce churn
- **Monitoring Recommendations**: KPIs to track ongoing
- **Predictive Actions**: Proactive retention strategies

## 📁 File Structure

```
Py/
├── churn_analysis_berezovsky.py      # Main analysis script
├── churn_data_preparation.py         # Data preparation utilities
├── run_churn_analysis.py            # Execution script
└── churn_visualization.py           # Additional visualization functions

Pics/
├── churn_analysis_comprehensive.png  # Main visualization output
└── interactive_cohort_heatmap.html  # Interactive cohort analysis

Data/
├── orders_info_ru_01-2025.csv       # January data
├── orders_info_ru_02-2025.csv       # February data
├── ...                              # Additional monthly files
└── customer_features_prepared.csv   # Processed customer data
```

## 🔧 Installation & Setup

### Prerequisites
```bash
pip install pandas numpy matplotlib seaborn plotly scikit-learn scipy
```

### Required Libraries
- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computations
- **matplotlib/seaborn**: Static visualizations
- **plotly**: Interactive visualizations
- **scikit-learn**: Machine learning utilities
- **scipy**: Statistical functions

## 🚀 Usage

### Quick Start
```python
from churn_analysis_berezovsky import ChurnAnalysisBerezovsky

# Initialize analyzer
analyzer = ChurnAnalysisBerezovsky(data_path='Data/')

# Run complete analysis
analyzer.run_complete_analysis(churn_threshold_days=60)
```

### Step-by-Step Execution
```python
# 1. Load and prepare data
analyzer.load_and_prepare_data()

# 2. Define churn (60 days inactivity)
analyzer.define_churn(churn_threshold_days=60)

# 3. Perform cohort analysis
analyzer.cohort_analysis()

# 4. Calculate churn rates
analyzer.calculate_churn_rates()

# 5. Segment customers
analyzer.segmentation_analysis()

# 6. Analyze time patterns
analyzer.time_based_analysis()

# 7. Identify predictive indicators
analyzer.identify_predictive_indicators()

# 8. Create visualizations
analyzer.create_visualizations()

# 9. Generate insights report
analyzer.generate_insights_report()
```

### Command Line Execution
```bash
cd Py/
python churn_analysis_berezovsky.py
```

## 📊 Key Metrics & Formulas

### Berezovsky's Churn Rate Formulas

1. **Simple Churn Rate**
   ```
   Simple Churn Rate = Customers Lost During Period / Customers at Start of Period
   ```

2. **Adjusted Churn Rate**
   ```
   Adjusted Churn Rate = Customers Lost / (Customers at Start + New Customers)
   ```

3. **Retention Rate**
   ```
   Retention Rate = 1 - Simple Churn Rate
   ```

### Customer Segmentation

1. **RFM Segmentation**
   - **Recency**: Days since last order
   - **Frequency**: Total number of orders
   - **Monetary**: Total amount spent

2. **Lifecycle Segmentation**
   - **One-time Buyer**: 1 order
   - **Occasional Buyer**: 2-3 orders
   - **Regular Customer**: 4-10 orders
   - **Loyal Customer**: 10+ orders

## 📈 Expected Outputs

### Visualizations
1. **Cohort Retention Heatmap**: Shows retention rates by acquisition cohort
2. **Monthly Churn Trends**: Simple vs. adjusted churn rates over time
3. **Customer Segmentation Charts**: Churn rates by various segments
4. **Predictive Indicators**: Correlation analysis with churn
5. **Interactive Dashboards**: Plotly-based interactive charts

### Reports
1. **Data Quality Report**: Data validation and cleaning summary
2. **Churn Analysis Summary**: Key metrics and trends
3. **Segmentation Insights**: Detailed breakdown by customer segments
4. **Actionable Recommendations**: Specific steps to reduce churn

## 🎯 Business Applications

### Immediate Actions
- Identify customers at risk of churning
- Implement targeted retention campaigns
- Improve onboarding for new customers

### Strategic Planning
- Develop region-specific retention strategies
- Create customer lifecycle management programs
- Build predictive churn models

### Performance Monitoring
- Track cohort retention monthly
- Monitor leading indicators
- Measure retention campaign effectiveness

## 📚 References & Further Reading

1. **Olga Berezovsky's Work**
   - "How to measure cohort retention" - Lenny's Newsletter
   - Data Analysis Journal - Substack publication
   - LinkedIn: @olgaberezovsky

2. **Methodology Sources**
   - Cohort Analysis best practices
   - Customer retention measurement
   - Churn prediction techniques

3. **Additional Resources**
   - "What is good retention" - Lenny's Newsletter
   - ChartMogul SaaS Metrics guides
   - Amplitude retention analysis documentation

## 🤝 Contributing

This analysis follows Olga Berezovsky's established methodology. For improvements or adaptations:

1. Maintain adherence to Berezovsky's core principles
2. Document any methodology modifications
3. Validate results against established benchmarks
4. Provide clear business justification for changes

## 📞 Support

For questions about the methodology or implementation:
- Review Olga Berezovsky's original publications
- Check the inline code documentation
- Refer to the generated analysis reports

---

**Note**: This implementation adapts Olga Berezovsky's methodology for transactional e-commerce data. The core principles remain faithful to her approach while being tailored for the specific business context of the Russian Orders Dataset.
