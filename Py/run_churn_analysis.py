#!/usr/bin/env python3
"""
Execute Comprehensive Churn Rate Analysis
Following <PERSON>'s Methodology

This script runs the complete churn analysis pipeline and generates
all visualizations and reports.

Usage:
    python run_churn_analysis.py

Author: AI Assistant following <PERSON>'s methodology
Date: July 2025
"""

import sys
import os
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from churn_analysis_<PERSON><PERSON><PERSON> import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from churn_data_preparation import ChurnDataPreparator
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure all required files are in the Py/ directory")
    sys.exit(1)

def check_requirements():
    """
    Check if all required libraries are installed
    """
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 
        'plotly', 'sklearn', 'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_data_files():
    """
    Check if required data files exist
    """
    data_path = 'Data/'
    required_files = [
        'orders_info_ru_01-2025.csv',
        'orders_info_ru_02-2025.csv',
        'orders_info_ru_03-2025.csv',
        'orders_info_ru_04-2025.csv',
        'orders_info_ru_05-2025.csv',
        'orders_info_ru_06-2025.csv'
    ]
    
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(os.path.join(data_path, file)):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required data files:")
        for file in missing_files:
            print(f"  - Data/{file}")
        return False
    
    return True

def create_output_directories():
    """
    Create necessary output directories
    """
    directories = ['Pics', 'Data']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Directory ready: {directory}/")

def run_analysis():
    """
    Execute the complete churn analysis
    """
    print("🚀 STARTING COMPREHENSIVE CHURN ANALYSIS")
    print("📊 Following Olga Berezovsky's Methodology")
    print("🗓️  Russian Orders Dataset (January - June 2025)")
    print("="*80)
    
    start_time = datetime.now()
    
    try:
        # Initialize the analyzer
        print("\n🔧 Initializing analyzer...")
        analyzer = ChurnAnalysisBerezovsky(data_path='Data/')
        
        # Run the complete analysis pipeline
        print("\n🎯 Executing analysis pipeline...")
        analyzer.run_complete_analysis(churn_threshold_days=60)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "="*80)
        print("🎉 ANALYSIS COMPLETED SUCCESSFULLY!")
        print("="*80)
        print(f"⏱️  Total execution time: {duration}")
        print(f"📊 Customers analyzed: {len(analyzer.customer_activity):,}")
        print(f"📈 Cohorts created: {len(analyzer.cohort_data['cohort_sizes']) if analyzer.cohort_data else 0}")
        
        print("\n📁 Generated Files:")
        print("  📊 Pics/churn_analysis_comprehensive.png - Main visualization")
        print("  🎨 Pics/interactive_cohort_heatmap.html - Interactive charts")
        print("  📋 Console output - Detailed insights and recommendations")
        
        print("\n🎯 Next Steps:")
        print("  1. Review the comprehensive visualization")
        print("  2. Open the interactive cohort heatmap in your browser")
        print("  3. Implement the recommended retention strategies")
        print("  4. Set up monitoring for the identified leading indicators")
        
        return analyzer
        
    except Exception as e:
        print(f"\n❌ Error during analysis: {str(e)}")
        print("Please check the error details and try again.")
        raise

def main():
    """
    Main execution function
    """
    print("🎯 CHURN ANALYSIS EXECUTION SCRIPT")
    print("📊 Olga Berezovsky's Methodology Implementation")
    print("="*60)
    
    # Pre-flight checks
    print("\n🔍 Running pre-flight checks...")
    
    if not check_requirements():
        print("❌ Requirements check failed. Please install missing packages.")
        return None
    
    if not check_data_files():
        print("❌ Data files check failed. Please ensure all data files are present.")
        return None
    
    print("✅ All pre-flight checks passed!")
    
    # Create output directories
    print("\n📁 Setting up output directories...")
    create_output_directories()
    
    # Run the analysis
    analyzer = run_analysis()
    
    return analyzer

if __name__ == "__main__":
    try:
        analyzer = main()
        if analyzer:
            print("\n✅ Analysis execution completed successfully!")
            print("📊 Check the generated files for detailed results.")
    except KeyboardInterrupt:
        print("\n⚠️  Analysis interrupted by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        print("Please check the error details and contact support if needed.")
