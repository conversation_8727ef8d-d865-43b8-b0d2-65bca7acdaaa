#!/usr/bin/env python3
"""
Churn Analysis Data Preparation Utilities
Supporting <PERSON>'s Churn Analysis Methodology

This module provides utility functions for data preparation and validation
specifically designed for churn analysis following <PERSON><PERSON><PERSON>'s best practices.

Author: AI Assistant following <PERSON>'s methodology
Date: July 2025
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

class ChurnDataPreparator:
    """
    Data preparation utilities for churn analysis following <PERSON><PERSON><PERSON>'s methodology
    """
    
    def __init__(self):
        """Initialize the data preparator"""
        self.data_quality_report = {}
    
    def validate_data_quality(self, df):
        """
        Validate data quality following <PERSON><PERSON><PERSON>'s data quality principles
        
        Args:
            df (pd.DataFrame): Input dataframe
            
        Returns:
            dict: Data quality report
        """
        print("🔍 VALIDATING DATA QUALITY")
        print("-" * 40)
        
        report = {
            'total_records': len(df),
            'missing_values': {},
            'data_types': {},
            'date_ranges': {},
            'outliers': {},
            'duplicates': 0
        }
        
        # Check missing values
        missing_counts = df.isnull().sum()
        report['missing_values'] = missing_counts[missing_counts > 0].to_dict()
        
        # Check data types
        report['data_types'] = df.dtypes.to_dict()
        
        # Check for duplicates
        report['duplicates'] = df.duplicated().sum()
        
        # Date range validation
        date_columns = ['order_date', 'buyer_registered', 'seller_registered']
        for col in date_columns:
            if col in df.columns:
                try:
                    date_series = pd.to_datetime(df[col], format='%d.%m.%Y', errors='coerce')
                    report['date_ranges'][col] = {
                        'min_date': date_series.min(),
                        'max_date': date_series.max(),
                        'invalid_dates': date_series.isnull().sum()
                    }
                except:
                    report['date_ranges'][col] = 'Error parsing dates'
        
        # Outlier detection for numerical columns
        numerical_cols = ['sum', 'views_count', 'seller_rating', 'buyer_rating']
        for col in numerical_cols:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                report['outliers'][col] = {
                    'count': len(outliers),
                    'percentage': (len(outliers) / len(df)) * 100,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound
                }
        
        self.data_quality_report = report
        
        # Print summary
        print(f"📊 Total records: {report['total_records']:,}")
        print(f"🔍 Duplicates: {report['duplicates']:,}")
        print(f"❌ Missing values: {len(report['missing_values'])} columns affected")
        
        if report['missing_values']:
            for col, count in report['missing_values'].items():
                percentage = (count / report['total_records']) * 100
                print(f"  - {col}: {count:,} ({percentage:.1f}%)")
        
        return report
    
    def clean_transactional_data(self, df):
        """
        Clean transactional data following Berezovsky's cleaning methodology
        
        Args:
            df (pd.DataFrame): Raw transactional data
            
        Returns:
            pd.DataFrame: Cleaned data
        """
        print("\n🧹 CLEANING TRANSACTIONAL DATA")
        print("-" * 40)
        
        df_clean = df.copy()
        original_count = len(df_clean)
        
        # 1. Convert date columns
        print("📅 Converting date columns...")
        date_columns = ['order_date', 'buyer_registered', 'seller_registered']
        for col in date_columns:
            if col in df_clean.columns:
                df_clean[col] = pd.to_datetime(df_clean[col], format='%d.%m.%Y', errors='coerce')
        
        # 2. Remove records with invalid order dates (critical for churn analysis)
        df_clean = df_clean.dropna(subset=['order_date'])
        print(f"✅ Removed {original_count - len(df_clean):,} records with invalid order dates")
        
        # 3. Filter out cancelled and refunded orders (following Berezovsky's "active" definition)
        active_filter = (df_clean['order_cancelled'] == 'no') & (df_clean['refunded'] == 'no')
        df_clean = df_clean[active_filter]
        print(f"✅ Filtered to active orders: {len(df_clean):,} records")
        
        # 4. Remove extreme outliers in order amounts
        Q1 = df_clean['sum'].quantile(0.25)
        Q3 = df_clean['sum'].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outlier_filter = (df_clean['sum'] >= lower_bound) & (df_clean['sum'] <= upper_bound)
        outliers_removed = len(df_clean) - len(df_clean[outlier_filter])
        df_clean = df_clean[outlier_filter]
        print(f"✅ Removed {outliers_removed:,} extreme outliers in order amounts")
        
        # 5. Ensure buyer_id and seller_id are not null (essential for churn analysis)
        df_clean = df_clean.dropna(subset=['buyer_id', 'seller_id'])
        
        # 6. Add derived columns for analysis
        df_clean['order_year'] = df_clean['order_date'].dt.year
        df_clean['order_month'] = df_clean['order_date'].dt.month
        df_clean['order_day_of_week'] = df_clean['order_date'].dt.day_name()
        df_clean['order_week'] = df_clean['order_date'].dt.isocalendar().week
        
        final_count = len(df_clean)
        retention_rate = (final_count / original_count) * 100
        
        print(f"📊 Data cleaning summary:")
        print(f"  Original records: {original_count:,}")
        print(f"  Final records: {final_count:,}")
        print(f"  Retention rate: {retention_rate:.1f}%")
        
        return df_clean
    
    def create_customer_features(self, df):
        """
        Create customer-level features for churn analysis
        Following Berezovsky's feature engineering approach
        
        Args:
            df (pd.DataFrame): Cleaned transactional data
            
        Returns:
            pd.DataFrame: Customer-level features
        """
        print("\n👤 CREATING CUSTOMER FEATURES")
        print("-" * 40)
        
        # Basic customer aggregations
        customer_features = df.groupby('buyer_id').agg({
            'order_date': ['min', 'max', 'count'],
            'sum': ['sum', 'mean', 'std', 'min', 'max'],
            'region_id': 'first',
            'buyer_registered': 'first',
            'views_count': 'mean',
            'buyer_rating': 'first'
        }).reset_index()
        
        # Flatten column names
        customer_features.columns = [
            'buyer_id', 'first_order_date', 'last_order_date', 'total_orders',
            'total_spent', 'avg_order_value', 'std_order_value', 'min_order_value', 'max_order_value',
            'region_id', 'buyer_registered', 'avg_views', 'buyer_rating'
        ]
        
        # Calculate derived features
        customer_features['customer_lifespan_days'] = (
            customer_features['last_order_date'] - customer_features['first_order_date']
        ).dt.days
        
        customer_features['days_since_last_order'] = (
            datetime.now() - customer_features['last_order_date']
        ).dt.days
        
        customer_features['order_frequency'] = customer_features['total_orders'] / (
            customer_features['customer_lifespan_days'] + 1
        )  # +1 to avoid division by zero
        
        customer_features['days_since_registration'] = (
            customer_features['first_order_date'] - customer_features['buyer_registered']
        ).dt.days
        
        # Customer value segmentation
        customer_features['value_quartile'] = pd.qcut(
            customer_features['total_spent'], 
            q=4, 
            labels=['Low', 'Medium-Low', 'Medium-High', 'High']
        )
        
        customer_features['frequency_quartile'] = pd.qcut(
            customer_features['total_orders'], 
            q=4, 
            labels=['Low', 'Medium-Low', 'Medium-High', 'High']
        )
        
        # Fill NaN values
        customer_features['std_order_value'] = customer_features['std_order_value'].fillna(0)
        customer_features['days_since_registration'] = customer_features['days_since_registration'].fillna(0)
        
        print(f"✅ Created features for {len(customer_features):,} unique customers")
        print(f"📊 Feature columns: {len(customer_features.columns)}")
        
        return customer_features
    
    def create_cohort_structure(self, customer_features, df):
        """
        Create cohort structure for retention analysis
        Following Berezovsky's cohort methodology
        
        Args:
            customer_features (pd.DataFrame): Customer-level features
            df (pd.DataFrame): Transaction data
            
        Returns:
            pd.DataFrame: Cohort structure data
        """
        print("\n📈 CREATING COHORT STRUCTURE")
        print("-" * 40)
        
        # Create acquisition cohorts (by month)
        customer_features['cohort_month'] = customer_features['first_order_date'].dt.to_period('M')
        
        # Create detailed cohort data for each customer-order combination
        cohort_data = []
        
        for _, customer in customer_features.iterrows():
            buyer_id = customer['buyer_id']
            cohort_month = customer['cohort_month']
            
            # Get all orders for this customer
            customer_orders = df[df['buyer_id'] == buyer_id]['order_date']
            
            for order_date in customer_orders:
                period_number = (order_date.to_period('M') - cohort_month).n
                cohort_data.append({
                    'buyer_id': buyer_id,
                    'cohort_month': cohort_month,
                    'order_date': order_date,
                    'period_number': period_number,
                    'order_month': order_date.to_period('M')
                })
        
        cohort_df = pd.DataFrame(cohort_data)
        
        print(f"✅ Created cohort structure with {len(cohort_df):,} customer-period combinations")
        print(f"📊 Cohorts: {cohort_df['cohort_month'].nunique()}")
        print(f"📅 Period range: 0 to {cohort_df['period_number'].max()} months")
        
        return cohort_df
    
    def export_prepared_data(self, customer_features, cohort_df, df_clean, output_path='Data/'):
        """
        Export prepared data for analysis
        
        Args:
            customer_features (pd.DataFrame): Customer features
            cohort_df (pd.DataFrame): Cohort data
            df_clean (pd.DataFrame): Cleaned transaction data
            output_path (str): Output directory path
        """
        print(f"\n💾 EXPORTING PREPARED DATA")
        print("-" * 40)
        
        import os
        os.makedirs(output_path, exist_ok=True)
        
        # Export datasets
        customer_features.to_csv(f'{output_path}customer_features_prepared.csv', index=False)
        cohort_df.to_csv(f'{output_path}cohort_data_prepared.csv', index=False)
        df_clean.to_csv(f'{output_path}transactions_cleaned.csv', index=False)
        
        print(f"✅ Exported prepared datasets to {output_path}")
        print(f"  - customer_features_prepared.csv ({len(customer_features):,} records)")
        print(f"  - cohort_data_prepared.csv ({len(cohort_df):,} records)")
        print(f"  - transactions_cleaned.csv ({len(df_clean):,} records)")


def main():
    """
    Main function for data preparation
    """
    print("🔧 CHURN ANALYSIS DATA PREPARATION")
    print("📊 Following Olga Berezovsky's Methodology")
    print("="*60)
    
    preparator = ChurnDataPreparator()
    
    # This would be called from the main analysis script
    print("✅ Data preparation utilities ready")
    print("📋 Use ChurnDataPreparator class in your main analysis")
    
    return preparator


if __name__ == "__main__":
    main()
