#!/usr/bin/env python3
"""
Comprehensive Churn Rate Analysis Following <PERSON>'s Methodology
Russian Orders Dataset (January - June 2025)

This script implements <PERSON><PERSON><PERSON>'s comprehensive approach to churn analysis:
1. Data Preparation and Customer Definition
2. Churn Definition for Transactional Business
3. Cohort-based Retention Analysis
4. Churn Rate Calculations (Simple & Adjusted)
5. Customer Segmentation Analysis
6. Time-based Pattern Analysis
7. Predictive Indicators Identification
8. Comprehensive Visualizations
9. Actionable Business Insights

Author: AI Assistant following <PERSON>'s methodology
Date: July 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime, timedelta
import glob
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Configure settings
warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 10)
plt.rcParams['font.size'] = 12

class ChurnAnalysisB<PERSON><PERSON>:
    """
    Comprehensive Churn Analysis following <PERSON> <PERSON>rez<PERSON>'s methodology
    Adapted for transactional e-commerce data
    """
    
    def __init__(self, data_path='Data/'):
        """
        Initialize the churn analysis with data path
        
        Args:
            data_path (str): Path to the data directory
        """
        self.data_path = data_path
        self.df = None
        self.customer_activity = None
        self.cohort_data = None
        self.churn_rates = None
        self.segments = None
        
        print("🚀 Initializing Comprehensive Churn Analysis")
        print("📊 Following Olga Berezovsky's Methodology")
        print("=" * 60)
    
    def load_and_prepare_data(self):
        """
        Step 1: Data Preparation - Load and examine dataset
        Following Berezovsky's approach to data preparation
        """
        print("\n📊 STEP 1: DATA PREPARATION")
        print("-" * 40)
        
        # Load all CSV files
        data_files = glob.glob(f'{self.data_path}orders_info_ru_*.csv')
        data_files.sort()
        
        print(f"Found {len(data_files)} data files:")
        for file in data_files:
            print(f"  - {file}")
        
        dfs = []
        for file in data_files:
            df_temp = pd.read_csv(file)
            dfs.append(df_temp)
        
        # Combine all data
        self.df = pd.concat(dfs, ignore_index=True)
        print(f"\n✅ Loaded {len(self.df):,} total records")
        
        # Data cleaning and preparation
        self._clean_data()
        self._define_customer_activity()
        
        return self.df
    
    def _clean_data(self):
        """
        Clean and prepare data following Berezovsky's data quality principles
        """
        print("\n🧹 Cleaning data...")
        
        # Convert date columns
        self.df['order_date'] = pd.to_datetime(self.df['order_date'], format='%d.%m.%Y')
        self.df['buyer_registered'] = pd.to_datetime(self.df['buyer_registered'], format='%d.%m.%Y', errors='coerce')
        self.df['seller_registered'] = pd.to_datetime(self.df['seller_registered'], format='%d.%m.%Y', errors='coerce')
        
        # Remove cancelled and refunded orders for active customer definition
        active_orders = self.df[
            (self.df['order_cancelled'] == 'no') & 
            (self.df['refunded'] == 'no')
        ].copy()
        
        # Filter out extreme outliers in order amounts (following Berezovsky's data quality approach)
        Q1 = active_orders['sum'].quantile(0.25)
        Q3 = active_orders['sum'].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        self.df = active_orders[
            (active_orders['sum'] >= lower_bound) & 
            (active_orders['sum'] <= upper_bound)
        ].copy()
        
        print(f"✅ After cleaning: {len(self.df):,} records")
        print(f"📅 Date range: {self.df['order_date'].min()} to {self.df['order_date'].max()}")
    
    def _define_customer_activity(self):
        """
        Step 2: Define "Active Customer" following Berezovsky's methodology
        For transactional business: Active = Made a purchase (main user action)
        """
        print("\n👤 Defining customer activity...")
        
        # Following Berezovsky's recommendation: use main user action (purchase) as activity
        # Create customer activity summary
        customer_summary = self.df.groupby('buyer_id').agg({
            'order_date': ['min', 'max', 'count'],
            'sum': ['sum', 'mean'],
            'region_id': 'first',
            'buyer_registered': 'first'
        }).reset_index()
        
        # Flatten column names
        customer_summary.columns = [
            'buyer_id', 'first_order_date', 'last_order_date', 'total_orders',
            'total_spent', 'avg_order_value', 'region_id', 'buyer_registered'
        ]
        
        # Calculate customer metrics
        customer_summary['customer_lifespan_days'] = (
            customer_summary['last_order_date'] - customer_summary['first_order_date']
        ).dt.days
        
        customer_summary['days_since_last_order'] = (
            datetime.now() - customer_summary['last_order_date']
        ).dt.days
        
        self.customer_activity = customer_summary
        print(f"✅ Analyzed {len(self.customer_activity):,} unique customers")
        
        return self.customer_activity
    
    def define_churn(self, churn_threshold_days=60):
        """
        Step 3: Define Churn for Transactional Business
        Following Berezovsky's approach adapted for e-commerce
        
        Args:
            churn_threshold_days (int): Days of inactivity to consider churned
        """
        print(f"\n🔄 STEP 2: CHURN DEFINITION")
        print("-" * 40)
        print(f"Churn threshold: {churn_threshold_days} days of inactivity")
        
        # Define churned customers based on inactivity
        self.customer_activity['is_churned'] = (
            self.customer_activity['days_since_last_order'] > churn_threshold_days
        )
        
        # Customer status classification
        def classify_customer_status(row):
            if row['days_since_last_order'] <= 30:
                return 'Active'
            elif row['days_since_last_order'] <= churn_threshold_days:
                return 'At Risk'
            else:
                return 'Churned'
        
        self.customer_activity['customer_status'] = self.customer_activity.apply(
            classify_customer_status, axis=1
        )
        
        # Print churn statistics
        churn_stats = self.customer_activity['customer_status'].value_counts()
        total_customers = len(self.customer_activity)
        
        print(f"\n📊 Customer Status Distribution:")
        for status, count in churn_stats.items():
            percentage = (count / total_customers) * 100
            print(f"  {status}: {count:,} ({percentage:.1f}%)")
        
        return self.customer_activity
    
    def cohort_analysis(self):
        """
        Step 4: Implement Cohort-based Analysis following Berezovsky's methodology
        Track customer retention over time periods
        """
        print(f"\n📈 STEP 3: COHORT ANALYSIS")
        print("-" * 40)
        
        # Create cohorts based on first order month (acquisition cohort)
        self.customer_activity['cohort_month'] = self.customer_activity['first_order_date'].dt.to_period('M')
        
        # Create period number for each customer's orders
        cohort_data = []
        
        for _, customer in self.customer_activity.iterrows():
            buyer_id = customer['buyer_id']
            cohort_month = customer['cohort_month']
            
            # Get all orders for this customer
            customer_orders = self.df[self.df['buyer_id'] == buyer_id]['order_date']
            
            for order_date in customer_orders:
                period_number = (order_date.to_period('M') - cohort_month).n
                cohort_data.append({
                    'buyer_id': buyer_id,
                    'cohort_month': cohort_month,
                    'order_date': order_date,
                    'period_number': period_number
                })
        
        cohort_df = pd.DataFrame(cohort_data)
        
        # Create cohort table
        cohort_table = cohort_df.groupby(['cohort_month', 'period_number'])['buyer_id'].nunique().reset_index()
        cohort_table = cohort_table.pivot(index='cohort_month', columns='period_number', values='buyer_id')
        
        # Calculate cohort sizes (customers acquired in each cohort)
        cohort_sizes = self.customer_activity.groupby('cohort_month')['buyer_id'].nunique()
        
        # Calculate retention rates (following Berezovsky's retention formula)
        cohort_retention = cohort_table.divide(cohort_sizes, axis=0)
        
        self.cohort_data = {
            'cohort_table': cohort_table,
            'cohort_sizes': cohort_sizes,
            'cohort_retention': cohort_retention
        }
        
        print(f"✅ Created cohort analysis for {len(cohort_sizes)} cohorts")
        print(f"📊 Tracking retention up to {cohort_retention.columns.max()} months")
        
        return self.cohort_data
    
    def calculate_churn_rates(self):
        """
        Step 5: Calculate Churn Rates using Berezovsky's formulas
        - Simple churn rate = (Customers lost during period) / (Customers at start of period)
        - Adjusted churn rate accounting for new acquisitions
        """
        print(f"\n📊 STEP 4: CHURN RATE CALCULATIONS")
        print("-" * 40)
        
        # Monthly churn rate calculation
        monthly_data = []
        
        # Get unique months in the data
        months = pd.date_range(
            start=self.df['order_date'].min().replace(day=1),
            end=self.df['order_date'].max().replace(day=1),
            freq='MS'
        )
        
        for i, month in enumerate(months[1:], 1):  # Start from second month
            prev_month = months[i-1]
            
            # Customers active in previous month
            prev_month_customers = set(
                self.df[
                    (self.df['order_date'] >= prev_month) & 
                    (self.df['order_date'] < month)
                ]['buyer_id']
            )
            
            # Customers active in current month
            current_month_customers = set(
                self.df[
                    (self.df['order_date'] >= month) & 
                    (self.df['order_date'] < month + pd.DateOffset(months=1))
                ]['buyer_id']
            )
            
            # New customers in current month
            new_customers = current_month_customers - prev_month_customers
            
            # Churned customers (were active last month, not active this month)
            churned_customers = prev_month_customers - current_month_customers
            
            # Berezovsky's churn rate formulas
            simple_churn_rate = len(churned_customers) / len(prev_month_customers) if prev_month_customers else 0
            
            # Adjusted churn rate (accounting for new acquisitions)
            total_potential_customers = len(prev_month_customers) + len(new_customers)
            adjusted_churn_rate = len(churned_customers) / total_potential_customers if total_potential_customers else 0
            
            monthly_data.append({
                'month': month,
                'customers_start': len(prev_month_customers),
                'customers_end': len(current_month_customers),
                'new_customers': len(new_customers),
                'churned_customers': len(churned_customers),
                'simple_churn_rate': simple_churn_rate,
                'adjusted_churn_rate': adjusted_churn_rate,
                'retention_rate': 1 - simple_churn_rate
            })
        
        self.churn_rates = pd.DataFrame(monthly_data)
        
        # Print summary statistics
        avg_simple_churn = self.churn_rates['simple_churn_rate'].mean()
        avg_adjusted_churn = self.churn_rates['adjusted_churn_rate'].mean()
        avg_retention = self.churn_rates['retention_rate'].mean()
        
        print(f"📊 Churn Rate Summary:")
        print(f"  Average Simple Churn Rate: {avg_simple_churn:.2%}")
        print(f"  Average Adjusted Churn Rate: {avg_adjusted_churn:.2%}")
        print(f"  Average Retention Rate: {avg_retention:.2%}")
        
        return self.churn_rates

    def segmentation_analysis(self):
        """
        Step 6: Customer Segmentation Analysis following Berezovsky's methodology
        Break down churn rates by customer segments
        """
        print(f"\n🎯 STEP 5: SEGMENTATION ANALYSIS")
        print("-" * 40)

        segments = {}

        # 1. Regional Segmentation
        regional_churn = self.customer_activity.groupby('region_id').agg({
            'is_churned': ['count', 'sum', 'mean'],
            'total_spent': 'mean',
            'total_orders': 'mean',
            'customer_lifespan_days': 'mean'
        }).round(3)

        regional_churn.columns = ['total_customers', 'churned_customers', 'churn_rate',
                                'avg_spent', 'avg_orders', 'avg_lifespan_days']
        segments['regional'] = regional_churn.sort_values('churn_rate', ascending=False)

        # 2. Customer Value Segmentation (RFM-inspired)
        # Recency, Frequency, Monetary segmentation
        self.customer_activity['recency_score'] = pd.qcut(
            self.customer_activity['days_since_last_order'],
            q=4, labels=['High', 'Medium-High', 'Medium-Low', 'Low']
        )

        self.customer_activity['frequency_score'] = pd.qcut(
            self.customer_activity['total_orders'],
            q=4, labels=['Low', 'Medium-Low', 'Medium-High', 'High']
        )

        self.customer_activity['monetary_score'] = pd.qcut(
            self.customer_activity['total_spent'],
            q=4, labels=['Low', 'Medium-Low', 'Medium-High', 'High']
        )

        # RFM Segmentation
        rfm_churn = self.customer_activity.groupby(['recency_score', 'frequency_score', 'monetary_score']).agg({
            'is_churned': ['count', 'sum', 'mean']
        }).round(3)

        rfm_churn.columns = ['total_customers', 'churned_customers', 'churn_rate']
        segments['rfm'] = rfm_churn.sort_values('churn_rate', ascending=False)

        # 3. Customer Lifecycle Segmentation
        def classify_customer_lifecycle(row):
            if row['total_orders'] == 1:
                return 'One-time Buyer'
            elif row['total_orders'] <= 3:
                return 'Occasional Buyer'
            elif row['total_orders'] <= 10:
                return 'Regular Customer'
            else:
                return 'Loyal Customer'

        self.customer_activity['lifecycle_segment'] = self.customer_activity.apply(
            classify_customer_lifecycle, axis=1
        )

        lifecycle_churn = self.customer_activity.groupby('lifecycle_segment').agg({
            'is_churned': ['count', 'sum', 'mean'],
            'total_spent': 'mean',
            'customer_lifespan_days': 'mean'
        }).round(3)

        lifecycle_churn.columns = ['total_customers', 'churned_customers', 'churn_rate',
                                 'avg_spent', 'avg_lifespan_days']
        segments['lifecycle'] = lifecycle_churn.sort_values('churn_rate', ascending=False)

        self.segments = segments

        # Print key insights
        print("🎯 Key Segmentation Insights:")
        print(f"  Highest churn region: {segments['regional'].index[0]} ({segments['regional'].iloc[0]['churn_rate']:.2%})")
        print(f"  Lowest churn region: {segments['regional'].index[-1]} ({segments['regional'].iloc[-1]['churn_rate']:.2%})")
        print(f"  Highest churn lifecycle: {segments['lifecycle'].index[0]} ({segments['lifecycle'].iloc[0]['churn_rate']:.2%})")

        return self.segments

    def time_based_analysis(self):
        """
        Step 7: Time-based Analysis - Analyze churn patterns across different time periods
        """
        print(f"\n📅 STEP 6: TIME-BASED ANALYSIS")
        print("-" * 40)

        time_analysis = {}

        # 1. Monthly Churn Trends
        monthly_trends = self.churn_rates.copy()
        monthly_trends['month_name'] = monthly_trends['month'].dt.strftime('%B %Y')
        time_analysis['monthly'] = monthly_trends

        # 2. Seasonal Analysis
        self.customer_activity['first_order_month'] = self.customer_activity['first_order_date'].dt.month
        self.customer_activity['first_order_season'] = self.customer_activity['first_order_month'].map({
            12: 'Winter', 1: 'Winter', 2: 'Winter',
            3: 'Spring', 4: 'Spring', 5: 'Spring',
            6: 'Summer', 7: 'Summer', 8: 'Summer',
            9: 'Fall', 10: 'Fall', 11: 'Fall'
        })

        seasonal_churn = self.customer_activity.groupby('first_order_season').agg({
            'is_churned': ['count', 'sum', 'mean'],
            'customer_lifespan_days': 'mean'
        }).round(3)

        seasonal_churn.columns = ['total_customers', 'churned_customers', 'churn_rate', 'avg_lifespan']
        time_analysis['seasonal'] = seasonal_churn.sort_values('churn_rate', ascending=False)

        # 3. Day of Week Analysis (for first orders)
        self.customer_activity['first_order_dow'] = self.customer_activity['first_order_date'].dt.day_name()

        dow_churn = self.customer_activity.groupby('first_order_dow').agg({
            'is_churned': ['count', 'sum', 'mean']
        }).round(3)

        dow_churn.columns = ['total_customers', 'churned_customers', 'churn_rate']
        time_analysis['day_of_week'] = dow_churn.sort_values('churn_rate', ascending=False)

        print("📅 Time-based Insights:")
        print(f"  Highest seasonal churn: {time_analysis['seasonal'].index[0]} ({time_analysis['seasonal'].iloc[0]['churn_rate']:.2%})")
        print(f"  Highest DOW churn: {time_analysis['day_of_week'].index[0]} ({time_analysis['day_of_week'].iloc[0]['churn_rate']:.2%})")

        return time_analysis

    def identify_predictive_indicators(self):
        """
        Step 8: Identify Leading Indicators and Early Warning Signs
        Following Berezovsky's approach to predictive analytics
        """
        print(f"\n🔮 STEP 7: PREDICTIVE INDICATORS")
        print("-" * 40)

        # Calculate correlation between customer metrics and churn
        numeric_columns = ['total_orders', 'total_spent', 'avg_order_value',
                          'customer_lifespan_days', 'days_since_last_order']

        correlations = {}
        for col in numeric_columns:
            if col in self.customer_activity.columns:
                corr = self.customer_activity[col].corr(self.customer_activity['is_churned'].astype(int))
                correlations[col] = corr

        # Sort by absolute correlation
        sorted_correlations = dict(sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True))

        print("🔮 Predictive Indicators (Correlation with Churn):")
        for indicator, corr in sorted_correlations.items():
            direction = "↑" if corr > 0 else "↓"
            print(f"  {indicator}: {corr:.3f} {direction}")

        # Early warning signs analysis
        # Customers at risk (not yet churned but showing warning signs)
        at_risk_customers = self.customer_activity[
            (self.customer_activity['customer_status'] == 'At Risk')
        ]

        warning_signs = {
            'customers_at_risk': len(at_risk_customers),
            'avg_days_since_last_order': at_risk_customers['days_since_last_order'].mean(),
            'avg_total_orders': at_risk_customers['total_orders'].mean(),
            'avg_order_value': at_risk_customers['avg_order_value'].mean()
        }

        print(f"\n⚠️  Early Warning Signs:")
        print(f"  Customers at risk: {warning_signs['customers_at_risk']:,}")
        print(f"  Avg days since last order: {warning_signs['avg_days_since_last_order']:.1f}")

        return {'correlations': sorted_correlations, 'warning_signs': warning_signs}

    def create_visualizations(self):
        """
        Step 9: Create Comprehensive Visualizations following Berezovsky's best practices
        """
        print(f"\n📊 STEP 8: CREATING VISUALIZATIONS")
        print("-" * 40)

        # Create figure with subplots
        fig = plt.figure(figsize=(20, 24))

        # 1. Cohort Retention Heatmap (Berezovsky's signature visualization)
        plt.subplot(4, 2, 1)
        if self.cohort_data and 'cohort_retention' in self.cohort_data:
            cohort_retention = self.cohort_data['cohort_retention']
            sns.heatmap(
                cohort_retention.iloc[:, :6],  # First 6 months
                annot=True, fmt='.2%', cmap='RdYlBu_r',
                cbar_kws={'label': 'Retention Rate'}
            )
            plt.title('Customer Cohort Retention Heatmap\n(Following Berezovsky Methodology)', fontsize=14, fontweight='bold')
            plt.xlabel('Period (Months)')
            plt.ylabel('Cohort (Acquisition Month)')

        # 2. Monthly Churn Rate Trends
        plt.subplot(4, 2, 2)
        if self.churn_rates is not None:
            plt.plot(self.churn_rates['month'], self.churn_rates['simple_churn_rate'],
                    marker='o', linewidth=2, label='Simple Churn Rate')
            plt.plot(self.churn_rates['month'], self.churn_rates['adjusted_churn_rate'],
                    marker='s', linewidth=2, label='Adjusted Churn Rate')
            plt.title('Monthly Churn Rate Trends\n(Berezovsky\'s Simple vs Adjusted)', fontsize=14, fontweight='bold')
            plt.xlabel('Month')
            plt.ylabel('Churn Rate')
            plt.legend()
            plt.xticks(rotation=45)
            plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))

        # 3. Customer Lifecycle Churn Analysis
        plt.subplot(4, 2, 3)
        if self.segments and 'lifecycle' in self.segments:
            lifecycle_data = self.segments['lifecycle']
            bars = plt.bar(lifecycle_data.index, lifecycle_data['churn_rate'],
                          color=['#ff6b6b', '#feca57', '#48dbfb', '#0abde3'])
            plt.title('Churn Rate by Customer Lifecycle Segment', fontsize=14, fontweight='bold')
            plt.xlabel('Customer Lifecycle Segment')
            plt.ylabel('Churn Rate')
            plt.xticks(rotation=45)
            plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))

            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.1%}', ha='center', va='bottom')

        # 4. Regional Churn Analysis (Top 10 regions)
        plt.subplot(4, 2, 4)
        if self.segments and 'regional' in self.segments:
            regional_data = self.segments['regional'].head(10)
            plt.barh(range(len(regional_data)), regional_data['churn_rate'],
                    color='coral')
            plt.title('Top 10 Regions by Churn Rate', fontsize=14, fontweight='bold')
            plt.xlabel('Churn Rate')
            plt.ylabel('Region ID')
            plt.yticks(range(len(regional_data)), regional_data.index)
            plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.1%}'.format(x)))

        # 5. Customer Status Distribution
        plt.subplot(4, 2, 5)
        status_counts = self.customer_activity['customer_status'].value_counts()
        colors = ['#2ecc71', '#f39c12', '#e74c3c']  # Green, Orange, Red
        plt.pie(status_counts.values, labels=status_counts.index, autopct='%1.1f%%',
                colors=colors, startangle=90)
        plt.title('Customer Status Distribution', fontsize=14, fontweight='bold')

        # 6. Days Since Last Order Distribution
        plt.subplot(4, 2, 6)
        plt.hist(self.customer_activity['days_since_last_order'], bins=50,
                color='skyblue', alpha=0.7, edgecolor='black')
        plt.axvline(x=60, color='red', linestyle='--', linewidth=2, label='Churn Threshold (60 days)')
        plt.title('Distribution of Days Since Last Order', fontsize=14, fontweight='bold')
        plt.xlabel('Days Since Last Order')
        plt.ylabel('Number of Customers')
        plt.legend()

        # 7. Customer Value vs Churn Scatter Plot
        plt.subplot(4, 2, 7)
        churned = self.customer_activity[self.customer_activity['is_churned']]
        active = self.customer_activity[~self.customer_activity['is_churned']]

        plt.scatter(active['total_orders'], active['total_spent'],
                   alpha=0.6, label='Active Customers', color='green', s=30)
        plt.scatter(churned['total_orders'], churned['total_spent'],
                   alpha=0.6, label='Churned Customers', color='red', s=30)
        plt.title('Customer Value vs Churn Analysis', fontsize=14, fontweight='bold')
        plt.xlabel('Total Orders')
        plt.ylabel('Total Spent (RUB)')
        plt.legend()
        plt.yscale('log')

        # 8. Retention Rate by Cohort (Line Chart)
        plt.subplot(4, 2, 8)
        if self.cohort_data and 'cohort_retention' in self.cohort_data:
            cohort_retention = self.cohort_data['cohort_retention']
            for cohort in cohort_retention.index[-5:]:  # Last 5 cohorts
                cohort_data = cohort_retention.loc[cohort].dropna()
                plt.plot(cohort_data.index, cohort_data.values,
                        marker='o', label=f'{cohort}', linewidth=2)

            plt.title('Retention Curves by Cohort\n(Last 5 Cohorts)', fontsize=14, fontweight='bold')
            plt.xlabel('Period (Months)')
            plt.ylabel('Retention Rate')
            plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))

        plt.tight_layout()
        plt.savefig('Pics/churn_analysis_comprehensive.png', dpi=300, bbox_inches='tight')
        print("✅ Comprehensive visualization saved to 'Pics/churn_analysis_comprehensive.png'")

        # Create additional Plotly interactive visualizations
        self._create_interactive_visualizations()

        plt.show()

    def _create_interactive_visualizations(self):
        """
        Create interactive Plotly visualizations for deeper analysis
        """
        print("🎨 Creating interactive visualizations...")

        # Interactive Cohort Heatmap
        if self.cohort_data and 'cohort_retention' in self.cohort_data:
            cohort_retention = self.cohort_data['cohort_retention'].fillna(0)

            fig = go.Figure(data=go.Heatmap(
                z=cohort_retention.values,
                x=[f'Month {i}' for i in cohort_retention.columns],
                y=[str(idx) for idx in cohort_retention.index],
                colorscale='RdYlBu_r',
                text=[[f'{val:.1%}' if val > 0 else '' for val in row] for row in cohort_retention.values],
                texttemplate='%{text}',
                textfont={"size": 10},
                hoverongaps=False
            ))

            fig.update_layout(
                title='Interactive Customer Cohort Retention Heatmap<br><sub>Following Olga Berezovsky\'s Methodology</sub>',
                xaxis_title='Period (Months)',
                yaxis_title='Cohort (Acquisition Month)',
                height=600
            )

            fig.write_html('Pics/interactive_cohort_heatmap.html')
            print("✅ Interactive cohort heatmap saved to 'Pics/interactive_cohort_heatmap.html'")

    def generate_insights_report(self):
        """
        Step 10: Generate Actionable Business Insights following Berezovsky's methodology
        """
        print(f"\n📋 STEP 9: GENERATING ACTIONABLE INSIGHTS")
        print("-" * 40)

        insights = []

        # Overall churn insights
        if self.churn_rates is not None:
            avg_churn = self.churn_rates['simple_churn_rate'].mean()
            insights.append(f"📊 Overall average churn rate: {avg_churn:.2%}")

            if avg_churn > 0.15:
                insights.append("⚠️  HIGH ALERT: Churn rate exceeds 15% - immediate action required")
            elif avg_churn > 0.10:
                insights.append("⚡ MODERATE RISK: Churn rate above 10% - monitor closely")
            else:
                insights.append("✅ HEALTHY: Churn rate below 10% - maintain current strategies")

        # Cohort insights
        if self.cohort_data and 'cohort_retention' in self.cohort_data:
            cohort_retention = self.cohort_data['cohort_retention']
            month_1_retention = cohort_retention.iloc[:, 1].mean()  # Month 1 retention
            insights.append(f"📈 Average Month 1 retention: {month_1_retention:.2%}")

            if month_1_retention < 0.30:
                insights.append("🚨 CRITICAL: Month 1 retention below 30% - onboarding issues likely")

        # Segmentation insights
        if self.segments:
            if 'lifecycle' in self.segments:
                lifecycle_data = self.segments['lifecycle']
                highest_churn_segment = lifecycle_data.index[0]
                highest_churn_rate = lifecycle_data.iloc[0]['churn_rate']
                insights.append(f"🎯 Highest churn segment: {highest_churn_segment} ({highest_churn_rate:.2%})")

            if 'regional' in self.segments:
                regional_data = self.segments['regional']
                highest_churn_region = regional_data.index[0]
                highest_churn_rate = regional_data.iloc[0]['churn_rate']
                insights.append(f"🌍 Highest churn region: {highest_churn_region} ({highest_churn_rate:.2%})")

        # Actionable recommendations
        recommendations = [
            "🎯 IMMEDIATE ACTIONS:",
            "1. Implement targeted retention campaigns for 'At Risk' customers",
            "2. Improve onboarding experience to boost Month 1 retention",
            "3. Create region-specific retention strategies for high-churn areas",
            "4. Develop loyalty programs for one-time buyers to encourage repeat purchases",
            "",
            "📊 MONITORING RECOMMENDATIONS:",
            "1. Set up automated alerts for customers approaching churn threshold",
            "2. Monitor cohort retention monthly using Berezovsky's methodology",
            "3. Track leading indicators: days since last order, order frequency",
            "4. Implement A/B testing for retention initiatives",
            "",
            "🔮 PREDICTIVE ACTIONS:",
            "1. Build machine learning models using identified predictive indicators",
            "2. Create customer health scores based on RFM analysis",
            "3. Implement proactive outreach for customers showing warning signs"
        ]

        # Combine insights and recommendations
        full_report = insights + [""] + recommendations

        # Print report
        print("\n" + "="*60)
        print("📋 COMPREHENSIVE CHURN ANALYSIS REPORT")
        print("Following Olga Berezovsky's Methodology")
        print("="*60)

        for item in full_report:
            print(item)

        print("="*60)

        return full_report

    def run_complete_analysis(self, churn_threshold_days=60):
        """
        Execute the complete churn analysis pipeline following Berezovsky's methodology

        Args:
            churn_threshold_days (int): Days of inactivity to consider churned
        """
        print("🚀 EXECUTING COMPLETE CHURN ANALYSIS PIPELINE")
        print("📊 Following Olga Berezovsky's Comprehensive Methodology")
        print("="*80)

        start_time = datetime.now()

        try:
            # Step 1: Data Preparation
            self.load_and_prepare_data()

            # Step 2: Churn Definition
            self.define_churn(churn_threshold_days)

            # Step 3: Cohort Analysis
            self.cohort_analysis()

            # Step 4: Churn Rate Calculations
            self.calculate_churn_rates()

            # Step 5: Segmentation Analysis
            self.segmentation_analysis()

            # Step 6: Time-based Analysis
            self.time_based_analysis()

            # Step 7: Predictive Indicators
            self.identify_predictive_indicators()

            # Step 8: Visualizations
            self.create_visualizations()

            # Step 9: Generate Insights Report
            self.generate_insights_report()

            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n✅ ANALYSIS COMPLETED SUCCESSFULLY!")
            print(f"⏱️  Total execution time: {duration}")
            print(f"📊 Analyzed {len(self.customer_activity):,} customers")
            print(f"📈 Generated comprehensive churn analysis following Berezovsky's methodology")

        except Exception as e:
            print(f"❌ Error during analysis: {str(e)}")
            raise


def main():
    """
    Main execution function for comprehensive churn analysis
    """
    print("🎯 COMPREHENSIVE CHURN RATE ANALYSIS")
    print("📊 Following Olga Berezovsky's Methodology")
    print("🗓️  Russian Orders Dataset (January - June 2025)")
    print("="*80)

    # Initialize analysis
    analyzer = ChurnAnalysisBerezovsky(data_path='Data/')

    # Run complete analysis
    analyzer.run_complete_analysis(churn_threshold_days=60)

    print("\n🎉 Analysis complete! Check the following outputs:")
    print("📊 Visualizations: Pics/churn_analysis_comprehensive.png")
    print("🎨 Interactive charts: Pics/interactive_cohort_heatmap.html")
    print("📋 Detailed insights printed above")

    return analyzer


if __name__ == "__main__":
    # Create Pics directory if it doesn't exist
    import os
    os.makedirs('Pics', exist_ok=True)

    # Run the analysis
    analyzer = main()
